#!/usr/bin/env python3
"""
简化的包测试脚本：验证 jss-api-extend 包是否正确打包
"""

import os
import sys
import zipfile
import tarfile
from pathlib import Path


def test_package_files():
    """测试包文件是否存在"""
    print("=" * 50)
    print("1. 检查包文件")
    print("=" * 50)
    
    wheel_file = "dist/jss_api_extend-0.1.0-py3-none-any.whl"
    tar_file = "dist/jss_api_extend-0.1.0.tar.gz"
    
    if os.path.exists(wheel_file):
        print(f"✅ Wheel 文件存在: {wheel_file}")
        print(f"   文件大小: {os.path.getsize(wheel_file)} bytes")
    else:
        print(f"❌ Wheel 文件不存在: {wheel_file}")
        return False
    
    if os.path.exists(tar_file):
        print(f"✅ Source 文件存在: {tar_file}")
        print(f"   文件大小: {os.path.getsize(tar_file)} bytes")
    else:
        print(f"❌ Source 文件不存在: {tar_file}")
        return False
    
    return True


def test_wheel_contents():
    """测试 wheel 文件内容"""
    print("\n" + "=" * 50)
    print("2. 检查 Wheel 文件内容")
    print("=" * 50)
    
    wheel_file = "dist/jss_api_extend-0.1.0-py3-none-any.whl"
    
    try:
        with zipfile.ZipFile(wheel_file, 'r') as z:
            files = z.namelist()
            
            print(f"✅ Wheel 文件包含 {len(files)} 个文件")
            
            # 检查主要模块
            main_modules = [
                'jss_api_extend/__init__.py',
                'jss_api_extend/adapter/',
                'jss_api_extend/client/',
                'jss_api_extend/common/',
                'jss_api_extend/utils/',
                'jss_api_extend/bitable/',
                'jss_api_extend/config/',
            ]
            
            print("\n主要模块检查:")
            for module in main_modules:
                found_files = [f for f in files if f.startswith(module)]
                if found_files:
                    print(f"✅ {module}: {len(found_files)} 个文件")
                else:
                    print(f"❌ {module}: 未找到")
            
            # 检查元数据文件
            metadata_files = [f for f in files if f.startswith('jss_api_extend-0.1.0.dist-info/')]
            print(f"\n✅ 元数据文件: {len(metadata_files)} 个")
            for meta_file in metadata_files:
                print(f"   - {meta_file}")
            
            return True
            
    except Exception as e:
        print(f"❌ 读取 wheel 文件失败: {e}")
        return False


def test_metadata():
    """测试包元数据"""
    print("\n" + "=" * 50)
    print("3. 检查包元数据")
    print("=" * 50)
    
    wheel_file = "dist/jss_api_extend-0.1.0-py3-none-any.whl"
    
    try:
        with zipfile.ZipFile(wheel_file, 'r') as z:
            # 读取 METADATA 文件
            metadata_content = z.read('jss_api_extend-0.1.0.dist-info/METADATA').decode('utf-8')
            
            # 检查关键信息
            key_info = {
                'Name': 'jss-api-extend',
                'Version': '0.1.0',
                'Requires-Python': '>=3.8',
                'License': 'MIT',
            }
            
            print("关键元数据检查:")
            for key, expected in key_info.items():
                if f"{key}: {expected}" in metadata_content or f"{key} {expected}" in metadata_content:
                    print(f"✅ {key}: {expected}")
                else:
                    print(f"❌ {key}: 未找到或不匹配")
            
            # 检查依赖项
            dependencies = [
                'requests>=2.25.0',
                'python-dateutil>=2.8.0',
                'tikhub>=1.0.0',
                'lark-oapi>=1.0.0',
            ]
            
            print("\n依赖项检查:")
            for dep in dependencies:
                if dep in metadata_content:
                    print(f"✅ {dep}")
                else:
                    print(f"❌ {dep}")
            
            # 显示完整的 METADATA（前50行）
            print("\n完整 METADATA 内容（前50行）:")
            print("-" * 40)
            lines = metadata_content.split('\n')
            for i, line in enumerate(lines[:50]):
                print(f"{i+1:2d}: {line}")
            if len(lines) > 50:
                print(f"... 还有 {len(lines) - 50} 行")
            
            return True
            
    except Exception as e:
        print(f"❌ 读取元数据失败: {e}")
        return False


def test_source_package():
    """测试源码包"""
    print("\n" + "=" * 50)
    print("4. 检查源码包")
    print("=" * 50)
    
    tar_file = "dist/jss_api_extend-0.1.0.tar.gz"
    
    try:
        with tarfile.open(tar_file, 'r:gz') as tar:
            files = tar.getnames()
            print(f"✅ 源码包包含 {len(files)} 个文件")
            
            # 检查重要文件
            important_files = [
                'jss_api_extend-0.1.0/pyproject.toml',
                'jss_api_extend-0.1.0/README.md',
                'jss_api_extend-0.1.0/LICENSE',
                'jss_api_extend-0.1.0/src/jss_api_extend/__init__.py',
            ]
            
            print("\n重要文件检查:")
            for file in important_files:
                if file in files:
                    print(f"✅ {file}")
                else:
                    print(f"❌ {file}")
            
            return True
            
    except Exception as e:
        print(f"❌ 读取源码包失败: {e}")
        return False


def test_import_simulation():
    """模拟导入测试"""
    print("\n" + "=" * 50)
    print("5. 模拟导入测试")
    print("=" * 50)
    
    # 检查 __init__.py 文件
    init_file = "src/jss_api_extend/__init__.py"
    if os.path.exists(init_file):
        print(f"✅ 主 __init__.py 文件存在")
        
        try:
            with open(init_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"   文件大小: {len(content)} 字符")
                
                # 检查是否有导出
                if '__all__' in content:
                    print("✅ 包含 __all__ 定义")
                else:
                    print("⚠️  未找到 __all__ 定义")
                
                # 显示前几行
                lines = content.split('\n')[:10]
                print("\n__init__.py 前10行:")
                for i, line in enumerate(lines, 1):
                    print(f"{i:2d}: {line}")
                    
        except Exception as e:
            print(f"❌ 读取 __init__.py 失败: {e}")
    else:
        print(f"❌ 主 __init__.py 文件不存在")
        return False
    
    # 检查子模块的 __init__.py 文件
    submodules = ['adapter', 'client', 'common', 'utils', 'bitable', 'config']
    print(f"\n子模块 __init__.py 检查:")
    for module in submodules:
        init_path = f"src/jss_api_extend/{module}/__init__.py"
        if os.path.exists(init_path):
            print(f"✅ {module}/__init__.py")
        else:
            print(f"❌ {module}/__init__.py")
    
    return True


def main():
    """主函数"""
    print("开始测试 jss-api-extend 包打包情况...")
    
    # 确保在正确的目录
    if not os.path.exists("pyproject.toml"):
        print("❌ 请在项目根目录运行此脚本")
        return 1
    
    tests = [
        test_package_files,
        test_wheel_contents,
        test_metadata,
        test_source_package,
        test_import_simulation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ 测试 {test.__name__} 失败")
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 出错: {e}")
    
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！包打包看起来正确。")
        print("\n建议的后续测试:")
        print("1. 在新的虚拟环境中安装包: pip install dist/jss_api_extend-0.1.0-py3-none-any.whl")
        print("2. 测试导入: python -c 'import jss_api_extend; print(jss_api_extend.__version__)'")
        print("3. 运行单元测试: pytest tests/")
        return 0
    else:
        print("❌ 部分测试失败，请检查包配置。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
