# 发布指南

本文档描述了如何发布 JSS API Extension 包到 PyPI。

## 📋 发布前检查清单

### 1. 代码质量检查
```bash
# 格式化代码
make format

# 运行代码检查
make lint

# 运行测试
make test
```

### 2. 版本更新
在 `pyproject.toml` 中更新版本号：
```toml
[project]
version = "x.y.z"
```

### 3. 更新文档
- 更新 `README.md` 中的版本信息
- 更新 `CHANGELOG.md`（如果有）
- 检查示例代码是否最新

## 🔨 构建包

### 清理旧的构建文件
```bash
make clean
```

### 构建包
```bash
make build
# 或者直接使用
python -m build
```

这将在 `dist/` 目录中生成：
- `jss_api_extend-x.y.z-py3-none-any.whl` (wheel 格式)
- `jss_api_extend-x.y.z.tar.gz` (源码包)

### 验证包
```bash
python -m twine check dist/*
```

## 🚀 发布到 PyPI

### 测试发布（推荐）
首先发布到 Test PyPI 进行测试：

```bash
# 发布到 Test PyPI
make upload-test
# 或者
python -m twine upload --repository testpypi dist/*
```

### 从 Test PyPI 安装测试
```bash
pip install --index-url https://test.pypi.org/simple/ jss-api-extend
```

### 正式发布
确认测试无误后，发布到正式 PyPI：

```bash
# 发布到 PyPI
make upload
# 或者
python -m twine upload dist/*
```

## 🔐 认证配置

### 方法1：使用 API Token（推荐）
1. 在 PyPI 创建 API Token
2. 配置 `~/.pypirc`：
```ini
[distutils]
index-servers = 
    pypi
    testpypi

[pypi]
repository = https://upload.pypi.org/legacy/
username = __token__
password = pypi-your-api-token

[testpypi]
repository = https://test.pypi.org/legacy/
username = __token__
password = pypi-your-test-api-token
```

### 方法2：环境变量
```bash
export TWINE_USERNAME=__token__
export TWINE_PASSWORD=pypi-your-api-token
```

## 📝 发布后步骤

### 1. 创建 Git 标签
```bash
git tag v0.1.0
git push origin v0.1.0
```

### 2. 创建 GitHub Release
在 GitHub 上创建对应的 Release，包含：
- 版本说明
- 主要更新内容
- 下载链接

### 3. 更新文档
- 更新安装说明
- 更新版本兼容性信息
- 通知用户新版本发布

## 🔄 版本管理策略

### 语义化版本控制
- `MAJOR.MINOR.PATCH` (例如: 1.2.3)
- `MAJOR`: 不兼容的 API 更改
- `MINOR`: 向后兼容的功能添加
- `PATCH`: 向后兼容的错误修复

### 预发布版本
- `1.0.0a1` (alpha)
- `1.0.0b1` (beta)
- `1.0.0rc1` (release candidate)

## 🛠️ 自动化发布（可选）

### GitHub Actions 示例
创建 `.github/workflows/publish.yml`：

```yaml
name: Publish to PyPI

on:
  release:
    types: [published]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine
    - name: Build package
      run: python -m build
    - name: Publish to PyPI
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: twine upload dist/*
```

## ⚠️ 注意事项

1. **不可撤销**: PyPI 上发布的版本无法删除，只能标记为 "yanked"
2. **版本唯一性**: 相同版本号无法重复上传
3. **测试充分**: 确保在 Test PyPI 上充分测试
4. **文档同步**: 确保文档与代码版本同步
5. **依赖检查**: 确认所有依赖都可用且版本正确

## 🔍 故障排除

### 常见错误
1. **403 Forbidden**: 检查认证信息和权限
2. **400 Bad Request**: 检查包元数据和格式
3. **文件已存在**: 版本号已被使用，需要更新版本

### 调试命令
```bash
# 详细输出
python -m twine upload --verbose dist/*

# 检查包内容
python -m zipfile -l dist/jss_api_extend-x.y.z-py3-none-any.whl
```

---

## 📞 支持

如果在发布过程中遇到问题，请：
1. 检查 PyPI 状态页面
2. 查看 Twine 文档
3. 提交 Issue 到项目仓库
