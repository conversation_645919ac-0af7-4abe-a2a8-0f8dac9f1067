# 发布指南

本文档描述了如何在 Windows 系统上发布 JSS API Extension 包到 PyPI。

## �️ Windows 环境准备

### 必需工具
确保已安装以下工具：

```cmd
# 检查 Python 版本（需要 3.8+）
python --version

# 安装/升级构建工具
python -m pip install --upgrade pip
python -m pip install --upgrade build twine

# 如果使用 Make（可选，需要安装 Make for Windows）
# 下载地址：http://gnuwin32.sourceforge.net/packages/make.htm
# 或者使用 Chocolatey：choco install make
```

### 推荐的开发环境
- **终端**: PowerShell 7+ 或 Windows Terminal
- **编辑器**: VS Code 或 PyCharm
- **包管理**: pip 或 conda

## �📋 发布前检查清单

### 1. 代码质量检查

**使用 Make（如果已安装）：**
```cmd
# 格式化代码
make format

# 运行代码检查
make lint

# 运行测试
make test
```

**使用 Windows 批处理脚本：**
```cmd
# 格式化代码
.\build.bat format

# 运行代码检查
.\build.bat lint

# 运行测试
.\build.bat test

# 运行完整检查（格式检查 + 代码检查 + 测试）
.\build.bat check
```

**使用 PowerShell 脚本：**
```powershell
# 格式化代码
.\build.ps1 format

# 运行代码检查
.\build.ps1 lint

# 运行测试
.\build.ps1 test

# 运行完整检查
.\build.ps1 check
```

**或者直接使用 Python 命令：**
```cmd
# 格式化代码
python -m black src tests examples
python -m isort src tests examples

# 运行代码检查
python -m flake8 src tests examples
python -m mypy src

# 运行测试
python -m pytest
```

### 2. 版本更新
在 `pyproject.toml` 中更新版本号：
```toml
[project]
version = "x.y.z"
```

### 3. 更新文档
- 更新 `README.md` 中的版本信息
- 更新 `CHANGELOG.md`（如果有）
- 检查示例代码是否最新

## 🔨 构建包

### 清理旧的构建文件

**使用 Make：**
```cmd
make clean
```

**或者手动清理（Windows PowerShell）：**
```powershell
# 删除构建目录
Remove-Item -Recurse -Force build -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force dist -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force *.egg-info -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force .pytest_cache -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force .mypy_cache -ErrorAction SilentlyContinue
Remove-Item -Force .coverage -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force htmlcov -ErrorAction SilentlyContinue

# 删除 Python 缓存文件
Get-ChildItem -Recurse -Name __pycache__ | Remove-Item -Recurse -Force
Get-ChildItem -Recurse -Name "*.pyc" | Remove-Item -Force
```

**或者使用 CMD：**
```cmd
rmdir /s /q build 2>nul
rmdir /s /q dist 2>nul
rmdir /s /q *.egg-info 2>nul
rmdir /s /q .pytest_cache 2>nul
rmdir /s /q .mypy_cache 2>nul
del /q .coverage 2>nul
rmdir /s /q htmlcov 2>nul
```

### 构建包

**使用 Make：**
```cmd
make build
```

**使用 Windows 批处理脚本：**
```cmd
.\build.bat build
```

**使用 PowerShell 脚本：**
```powershell
.\build.ps1 build
```

**或者直接使用 Python：**
```cmd
python -m build
```

这将在 `dist\` 目录中生成：
- `jss_api_extend-x.y.z-py3-none-any.whl` (wheel 格式)
- `jss_api_extend-x.y.z.tar.gz` (源码包)

### 验证包
```cmd
python -m twine check dist\*
```

## 🚀 发布到 PyPI

### 测试发布（推荐）
首先发布到 Test PyPI 进行测试：

**使用 Make：**
```cmd
make upload-test
```

**使用 Windows 批处理脚本：**
```cmd
.\build.bat upload-test
```

**使用 PowerShell 脚本：**
```powershell
.\build.ps1 upload-test
```

**或者直接使用 Python：**
```cmd
python -m twine upload --repository testpypi dist\*
```

### 从 Test PyPI 安装测试
```cmd
pip install --index-url https://test.pypi.org/simple/ jss-api-extend
```

### 正式发布
确认测试无误后，发布到正式 PyPI：

**使用 Make：**
```cmd
make upload
```

**使用 Windows 批处理脚本：**
```cmd
.\build.bat upload
```

**使用 PowerShell 脚本：**
```powershell
.\build.ps1 upload
```

**或者直接使用 Python：**
```cmd
python -m twine upload dist\*
```

## 🔐 认证配置

### 方法1：使用 API Token（推荐）
1. 在 PyPI 创建 API Token
2. 在 Windows 上配置 `.pypirc` 文件：

**文件位置：** `%USERPROFILE%\.pypirc` (通常是 `C:\Users\<USER>\.pypirc`)

```ini
[distutils]
index-servers =
    pypi
    testpypi

[pypi]
repository = https://upload.pypi.org/legacy/
username = __token__
password = pypi-your-api-token

[testpypi]
repository = https://test.pypi.org/legacy/
username = __token__
password = pypi-your-test-api-token
```

### 方法2：环境变量

**PowerShell：**
```powershell
$env:TWINE_USERNAME = "__token__"
$env:TWINE_PASSWORD = "pypi-your-api-token"
```

**CMD：**
```cmd
set TWINE_USERNAME=__token__
set TWINE_PASSWORD=pypi-your-api-token
```

**永久设置环境变量（PowerShell）：**
```powershell
[Environment]::SetEnvironmentVariable("TWINE_USERNAME", "__token__", "User")
[Environment]::SetEnvironmentVariable("TWINE_PASSWORD", "pypi-your-api-token", "User")
```

## 📝 发布后步骤

### 1. 创建 Git 标签
```cmd
git tag v0.1.0
git push origin v0.1.0
```

### 2. 创建 GitHub Release
在 GitHub 上创建对应的 Release，包含：
- 版本说明
- 主要更新内容
- 下载链接

### 3. 更新文档
- 更新安装说明
- 更新版本兼容性信息
- 通知用户新版本发布

## 🔄 版本管理策略

### 语义化版本控制
- `MAJOR.MINOR.PATCH` (例如: 1.2.3)
- `MAJOR`: 不兼容的 API 更改
- `MINOR`: 向后兼容的功能添加
- `PATCH`: 向后兼容的错误修复

### 预发布版本
- `1.0.0a1` (alpha)
- `1.0.0b1` (beta)
- `1.0.0rc1` (release candidate)

## 🛠️ 自动化发布（可选）

### GitHub Actions 示例
创建 `.github/workflows/publish.yml`：

```yaml
name: Publish to PyPI

on:
  release:
    types: [published]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine
    - name: Build package
      run: python -m build
    - name: Publish to PyPI
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: twine upload dist/*
```

## ⚠️ 注意事项

1. **不可撤销**: PyPI 上发布的版本无法删除，只能标记为 "yanked"
2. **版本唯一性**: 相同版本号无法重复上传
3. **测试充分**: 确保在 Test PyPI 上充分测试
4. **文档同步**: 确保文档与代码版本同步
5. **依赖检查**: 确认所有依赖都可用且版本正确

## 🔍 故障排除

### 常见错误
1. **403 Forbidden**: 检查认证信息和权限
2. **400 Bad Request**: 检查包元数据和格式
3. **文件已存在**: 版本号已被使用，需要更新版本

### 调试命令
```cmd
# 详细输出
python -m twine upload --verbose dist\*

# 检查包内容
python -m zipfile -l dist\jss_api_extend-x.y.z-py3-none-any.whl
```

## 🪟 Windows 特定问题解决

### 路径问题
- 使用反斜杠 `\` 作为路径分隔符
- 或者在 Python 中使用正斜杠 `/`（Python 会自动转换）
- 避免路径中包含空格，如果有空格请用引号包围

### 权限问题
如果遇到权限错误：
```powershell
# 以管理员身份运行 PowerShell
# 或者使用用户级安装
python -m pip install --user package_name
```

### 长路径问题
Windows 默认路径长度限制为 260 字符，如果遇到路径过长问题：
1. 启用长路径支持（Windows 10 1607+）
2. 或者将项目移到更短的路径下

### 编码问题
确保终端使用 UTF-8 编码：
```powershell
# PowerShell 中设置编码
$OutputEncoding = [console]::InputEncoding = [console]::OutputEncoding = New-Object System.Text.UTF8Encoding
```

### Make 工具替代方案
项目已提供 Windows 专用的构建脚本：

**批处理脚本 (`build.bat`)：**
```cmd
# 查看所有可用命令
build.bat help

# 常用命令
build.bat clean         # 清理构建文件
build.bat format        # 格式化代码
build.bat lint          # 代码检查
build.bat test          # 运行测试
build.bat build         # 构建包
build.bat upload-test   # 上传到测试 PyPI
build.bat upload        # 上传到正式 PyPI
```

**PowerShell 脚本 (`build.ps1`)：**
```powershell
# 查看所有可用命令
.\build.ps1 help

# 常用命令
.\build.ps1 clean         # 清理构建文件
.\build.ps1 format        # 格式化代码
.\build.ps1 lint          # 代码检查
.\build.ps1 test          # 运行测试
.\build.ps1 build         # 构建包
.\build.ps1 upload-test   # 上传到测试 PyPI
.\build.ps1 upload        # 上传到正式 PyPI
```

**注意：** 首次运行 PowerShell 脚本可能需要设置执行策略：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

---

## 📞 支持

如果在发布过程中遇到问题，请：
1. 检查 PyPI 状态页面
2. 查看 Twine 文档
3. 提交 Issue 到项目仓库
