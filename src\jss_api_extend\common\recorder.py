# -*- encoding:utf-8 -*-

from .service_exception import ServiceException
from ..utils.file_utils import FileUtils


class RecordFile:
    def __init__(self, file_path):
        self.file_path = file_path
        self.file = open(file_path, "w+", encoding='utf-8')
        if not self.file:
            raise ServiceException("Recorder open failed, file_path=%s" % file_path)

    def write(self, data: str):
        self.file.write(data)

    def write_line(self, data: str):
        data += '\n'
        self.file.write(data)

    def close(self):
        self.file.close()


class Recorder:
    def __init__(self, task_uuid):
        statistics_file_path = FileUtils.get_task_result_statistics_file_path(task_uuid)
        account_file_path = FileUtils.get_task_result_account_file_path(task_uuid)
        position_file_path = FileUtils.get_task_result_position_file_path(task_uuid)
        order_file_path = FileUtils.get_task_result_order_file_path(task_uuid)
        trade_file_path = FileUtils.get_task_result_trade_file_path(task_uuid)

        self.statistics_file = RecordFile(statistics_file_path)
        self.account_file = RecordFile(account_file_path)
        self.position_file = RecordFile(position_file_path)
        self.order_file = RecordFile(order_file_path)
        self.trade_file = RecordFile(trade_file_path)

    def record_statistics(self, statistics_info):
        self.statistics_file.write(statistics_info)

    def record_trade(self, trade_info):
        self.trade_file.write_line(trade_info)

    def record_order(self, order_info):
        self.order_file.write_line(order_info)

    def record_account(self, account_info):
        self.account_file.write_line(account_info)

    def record_position(self, position_info):
        self.position_file.write_line(position_info)

    def close(self):
        self.statistics_file.close()
        self.account_file.close()
        self.position_file.close()
        self.order_file.close()
        self.trade_file.close()
