# -*- encoding:utf-8 -*-

import json

from common.decimal_encoder import DecimalEncoder
from common.error_code import ErrorCode


class Response(object):
    @staticmethod
    def success(content=""):
        response = {
            "code": 0,
            "msg": "",
            "data": content
        }

        return json.dumps(response, ensure_ascii=True, cls=DecimalEncoder)

    @staticmethod
    def error(error_code: int):
        response = {
            "code": error_code,
            "msg": ErrorCode.get_error_msg(error_code),
            "data": ""
        }

        return json.dumps(response, ensure_ascii=True, cls=DecimalEncoder)\


    @staticmethod
    def error2(error_code: int, error_msg):
        response = {
            "code": error_code,
            "msg": error_msg,
            "data": ""
        }

        return json.dumps(response, ensure_ascii=True, cls=DecimalEncoder)

    @staticmethod
    def error3(error_code: int, error_msg, data):
        response = {
            "code": error_code,
            "msg": error_msg,
            "data": data
        }

        return json.dumps(response, ensure_ascii=True, cls=DecimalEncoder)
