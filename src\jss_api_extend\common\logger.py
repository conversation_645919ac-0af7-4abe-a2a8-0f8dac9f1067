# -*- encoding:utf-8 -*-

import logging
import os
import time

from logging.handlers import TimedRotatingFileHandler


class TimeFormatter(logging.Formatter):

    def __init__(self, fmt=None, datefmt=None, style='%'):
        super().__init__(fmt, datefmt, style)

    def formatTime(self, record, datefmt=None):
        self.default_msec_format = '%s.%03d'
        ct = self.converter(record.created)
        if datefmt:
            s = time.strftime(datefmt, ct)
        else:
            t = time.strftime(self.default_time_format, ct)
            s = self.default_msec_format % (t, record.msecs)
        return s


class TaskTimeFormatter(logging.Formatter):

    def __init__(self, fmt=None, datefmt=None, style='%'):
        super().__init__(fmt, datefmt, style)

    def formatTime(self, record, datefmt=None):
        self.default_msec_format = '%s'
        ct = self.converter(record.created)
        if datefmt:
            s = time.strftime(datefmt, ct)
        else:
            s = time.strftime(self.default_time_format, ct)
            # s = self.default_msec_format % (t, record.msecs)
        return s


FATAL = logging.FATAL
ERROR = logging.ERROR
WARNING = logging.WARNING
INFO = logging.INFO
DEBUG = logging.DEBUG


class Logger:
    def __init__(self, project_path, file_name, level=DEBUG, log_dir=None, is_task_log=False):
        file_path = None

        if log_dir:
            file_path = log_dir
        else:
            if not is_task_log:
                file_path = os.path.join(project_path, 'logs')

        if is_task_log:
            self.logger = self._init_task_logger(project_path, level)
        else:
            self.logger = self._init_logger(file_path, file_name, level)

    def _init_logger(self, file_path, file_name, level):
        if not os.path.exists(file_path):
            os.mkdir(file_path)

        file_path_ = os.path.join(file_path, file_name)

        fmt = '%(asctime)s %(levelname)s %(filename)s %(funcName)s Line:%(lineno)s %(thread)d - %(message)s'
        formatter = TimeFormatter(fmt)

        # 文件处理器
        log_engine_handler = logging.handlers.TimedRotatingFileHandler(file_path_,
                                                                       encoding='utf-8',
                                                                       when='D',
                                                                       interval=1,
                                                                       backupCount=0)
        log_engine_handler.suffix = '%Y-%m-%d.log'
        log_engine_handler.setLevel(level)
        log_engine_handler.setFormatter(formatter)

        # 终端处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)

        log = logging.getLogger(file_path_)
        log.addHandler(log_engine_handler)
        log.addHandler(console_handler)
        log.setLevel(level)

        return log

    def _init_task_logger(self, file_path, level):
        file_path_ = file_path

        fmt = '%(levelname)s %(asctime)s %(message)s'
        formatter = TaskTimeFormatter(fmt)

        # 文件处理器
        log_engine_handler = logging.handlers.RotatingFileHandler(file_path_, mode='a', encoding='utf-8', delay=False)
        log_engine_handler.setLevel(level)
        log_engine_handler.setFormatter(formatter)

        # 终端处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)

        log = logging.getLogger(file_path_)
        log.addHandler(log_engine_handler)
        log.addHandler(console_handler)
        log.setLevel(level)

        return log

    def log(self):
        return self.logger
