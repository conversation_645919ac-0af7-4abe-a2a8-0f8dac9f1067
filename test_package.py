#!/usr/bin/env python3
"""
测试脚本：验证 jss-api-extend 包是否正确打包
"""

import os
import sys
import subprocess
import tempfile
import shutil
import zipfile
import tarfile
from pathlib import Path


def test_package_structure():
    """测试包结构是否正确"""
    print("=" * 60)
    print("1. 测试包结构")
    print("=" * 60)
    
    # 检查 wheel 文件
    wheel_file = "dist/jss_api_extend-0.1.0-py3-none-any.whl"
    if not os.path.exists(wheel_file):
        print(f"❌ Wheel 文件不存在: {wheel_file}")
        return False
    
    print(f"✅ Wheel 文件存在: {wheel_file}")
    
    # 检查 tar.gz 文件
    tar_file = "dist/jss_api_extend-0.1.0.tar.gz"
    if not os.path.exists(tar_file):
        print(f"❌ Tar.gz 文件不存在: {tar_file}")
        return False
    
    print(f"✅ Tar.gz 文件存在: {tar_file}")
    
    # 检查 wheel 文件内容
    with zipfile.ZipFile(wheel_file, 'r') as z:
        files = z.namelist()
        
        # 检查必要的文件
        required_files = [
            'jss_api_extend/__init__.py',
            'jss_api_extend/adapter/__init__.py',
            'jss_api_extend/client/__init__.py',
            'jss_api_extend/common/__init__.py',
            'jss_api_extend/utils/__init__.py',
            'jss_api_extend-0.1.0.dist-info/METADATA',
            'jss_api_extend-0.1.0.dist-info/WHEEL',
        ]
        
        missing_files = []
        for required_file in required_files:
            if required_file not in files:
                missing_files.append(required_file)
        
        if missing_files:
            print(f"❌ Wheel 文件中缺少必要文件: {missing_files}")
            return False
        
        print(f"✅ Wheel 文件包含 {len(files)} 个文件")
        print("✅ 所有必要文件都存在")
    
    return True


def test_metadata():
    """测试包元数据是否正确"""
    print("\n" + "=" * 60)
    print("2. 测试包元数据")
    print("=" * 60)
    
    wheel_file = "dist/jss_api_extend-0.1.0-py3-none-any.whl"
    
    with zipfile.ZipFile(wheel_file, 'r') as z:
        # 读取 METADATA 文件
        metadata_content = z.read('jss_api_extend-0.1.0.dist-info/METADATA').decode('utf-8')
        
        # 检查关键元数据
        required_metadata = [
            'Name: jss-api-extend',
            'Version: 0.1.0',
            'Author: JSS Team',
            'Author-email: <EMAIL>',
            'License: MIT',
            'Requires-Python: >=3.8',
        ]
        
        missing_metadata = []
        for metadata in required_metadata:
            if metadata not in metadata_content:
                missing_metadata.append(metadata)
        
        if missing_metadata:
            print(f"❌ 缺少必要的元数据: {missing_metadata}")
            print("\n实际的 METADATA 内容:")
            print(metadata_content)
            return False
        
        print("✅ 所有必要的元数据都存在")
        
        # 检查依赖项
        dependencies = [
            'requests>=2.25.0',
            'python-dateutil>=2.8.0',
            'tikhub>=1.0.0',
            'lark-oapi>=1.0.0',
        ]
        
        for dep in dependencies:
            if dep not in metadata_content:
                print(f"⚠️  依赖项可能缺失: {dep}")
        
        print("✅ 元数据检查完成")
    
    return True


def test_installation():
    """测试包是否可以正确安装"""
    print("\n" + "=" * 60)
    print("3. 测试包安装")
    print("=" * 60)
    
    # 创建临时虚拟环境
    with tempfile.TemporaryDirectory() as temp_dir:
        venv_path = os.path.join(temp_dir, "test_venv")
        
        try:
            # 创建虚拟环境
            print("创建临时虚拟环境...")
            subprocess.run([sys.executable, "-m", "venv", venv_path], 
                         check=True, capture_output=True)
            
            # 确定 pip 路径
            if os.name == 'nt':  # Windows
                pip_path = os.path.join(venv_path, "Scripts", "pip.exe")
                python_path = os.path.join(venv_path, "Scripts", "python.exe")
            else:  # Unix/Linux/macOS
                pip_path = os.path.join(venv_path, "bin", "pip")
                python_path = os.path.join(venv_path, "bin", "python")
            
            # 升级 pip
            subprocess.run([python_path, "-m", "pip", "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            
            # 安装包
            wheel_file = os.path.abspath("dist/jss_api_extend-0.1.0-py3-none-any.whl")
            print(f"安装包: {wheel_file}")
            
            result = subprocess.run([pip_path, "install", wheel_file], 
                                  capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"❌ 包安装失败:")
                print(f"stdout: {result.stdout}")
                print(f"stderr: {result.stderr}")
                return False
            
            print("✅ 包安装成功")
            
            # 测试导入
            print("测试包导入...")
            import_test = subprocess.run([
                python_path, "-c", 
                "import jss_api_extend; print('导入成功:', jss_api_extend.__name__)"
            ], capture_output=True, text=True)
            
            if import_test.returncode != 0:
                print(f"❌ 包导入失败:")
                print(f"stdout: {import_test.stdout}")
                print(f"stderr: {import_test.stderr}")
                return False
            
            print("✅ 包导入成功")
            print(f"导入结果: {import_test.stdout.strip()}")
            
            # 测试子模块导入
            submodules = [
                "jss_api_extend.adapter",
                "jss_api_extend.client",
                "jss_api_extend.common",
                "jss_api_extend.utils",
            ]
            
            for module in submodules:
                test_result = subprocess.run([
                    python_path, "-c", f"import {module}; print('✅ {module} 导入成功')"
                ], capture_output=True, text=True)
                
                if test_result.returncode != 0:
                    print(f"❌ 子模块 {module} 导入失败:")
                    print(f"stderr: {test_result.stderr}")
                    return False
                else:
                    print(test_result.stdout.strip())
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 虚拟环境操作失败: {e}")
            return False


def test_package_info():
    """测试包信息"""
    print("\n" + "=" * 60)
    print("4. 测试包信息")
    print("=" * 60)
    
    try:
        # 使用 pip show 显示包信息
        result = subprocess.run([
            sys.executable, "-m", "pip", "show", "jss-api-extend"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 包信息:")
            print(result.stdout)
        else:
            print("⚠️  包未安装在当前环境中，这是正常的")
            
    except Exception as e:
        print(f"⚠️  获取包信息时出错: {e}")
    
    return True


def main():
    """主测试函数"""
    print("开始测试 jss-api-extend 包...")
    
    # 确保在正确的目录中
    if not os.path.exists("pyproject.toml"):
        print("❌ 请在项目根目录中运行此脚本")
        sys.exit(1)
    
    tests = [
        test_package_structure,
        test_metadata,
        test_installation,
        test_package_info,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ 测试 {test.__name__} 失败")
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 出错: {e}")
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！包打包正确。")
        return 0
    else:
        print("❌ 部分测试失败，请检查包配置。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
